# -*- coding: utf-8 -*-
"""Emotion-detection-using-Deep-Learning-LSTM.ipynb

Automatically generated by <PERSON><PERSON>.

Original file is located at
    https://colab.research.google.com/drive/1Rhk-Nk7PeTUJ3PljEyU3sF3z0owfpLRU
"""

from keras.models import Sequential
from keras.layers import Embedding, LSTM, Dense, Dropout
from keras.callbacks import EarlyStopping
from keras.preprocessing.sequence import pad_sequences
from keras.utils import to_categorical
from tensorflow.keras.preprocessing.text import one_hot

from sklearn.preprocessing import LabelEncoder
import pandas as pd
import numpy as np
import pickle
import nltk
import re
import nltk.stem as PorterStemme

import seaborn as sns
import matplotlib.pyplot as plt
from wordcloud import WordCloud

train_data = pd.read_csv("/content/train.txt", header = None , sep=';',names = ['Comment','Emotion'], encoding='utf-8')

train_data

train_data.shape

train_data['length'] = [len(x) for x in train_data['Comment']]

train_data

train_data.shape

train_data.isnull().sum()

train_data.duplicated().sum()

train_data.drop_duplicates(inplace=True)

train_data['Emotion'].value_counts()

sns.countplot(x='Emotion',data=train_data)
plt.show()

df2 = train_data.copy()
length_values = df2['length'].values

sns.histplot(data= df2,x='length',hue='Emotion',multiple = 'stack')
plt.show()

def words_cloud(wordcloud, df):
    plt.figure(figsize=(10, 10))
    plt.title(df+' Word Cloud', size = 16)
    plt.imshow(wordcloud)

    plt.axis("off");
emotions_list = train_data['Emotion'].unique()
for emotion in emotions_list:
    text = ' '.join([sentence for sentence in train_data.loc[train_data['Emotion'] == emotion,'Comment']])
    wordcloud = WordCloud(width = 600, height = 600).generate(text)
    words_cloud(wordcloud, emotion)

lb = LabelEncoder()
train_data['Emotion'] = lb.fit_transform(train_data['Emotion'])

train_data

train_data['length'].max()

import re
import nltk
from nltk.corpus import stopwords
from nltk.stem import PorterStemmer
from tensorflow.keras.preprocessing.text import one_hot
from tensorflow.keras.preprocessing.sequence import pad_sequences
from tensorflow.keras.utils import to_categorical


nltk.download('stopwords')

stop_words = set(stopwords.words('english'))
def text_cleaning(df, column, vocab_size, max_len):
    stemmer = PorterStemmer()
    corpus = []

    for text in df[column]:

        text = re.sub("[^a-zA-Z]", " ", text)
        text = text.lower()

        text = text.split()

        text = [stemmer.stem(word) for word in text if word not in stop_words]
        text = " ".join(text)
        corpus.append(text)

    one_hot_word = [one_hot(input_text=word, n=vocab_size) for word in corpus]

    pad = pad_sequences(sequences=one_hot_word, maxlen=max_len, padding='pre')
    return pad

# Text cleaning and encoding
x_train = text_cleaning(train_data, "Comment", vocab_size=11000, max_len=300)
y_train = to_categorical(train_data["Emotion"])

x_train

model = Sequential()
model.add(Embedding(input_dim=11000, output_dim=150, input_length=300))
model.add(Dropout(0.2))
model.add(LSTM(128))
model.add(Dropout(0.2))
model.add(Dense(64, activation='sigmoid'))
model.add(Dropout(0.2))
model.add(Dense(6, activation='softmax'))
model.compile(optimizer='adam', loss='categorical_crossentropy', metrics=['accuracy'])
callback = EarlyStopping(monitor="val_loss", patience=2, restore_best_weights=True)
model.fit(x_train, y_train, epochs=10, batch_size=64, verbose=1, callbacks=[callback])

# Text cleaning function
def sentence_cleaning(sentence):
    stemmer = PorterStemmer()
    corpus = []
    text = re.sub("[^a-zA-Z]", " ", sentence)
    text = text.lower()
    text = text.split()
    text = [stemmer.stem(word) for word in text if word not in stopwords]
    text = " ".join(text)
    corpus.append(text)
    one_hot_word = [one_hot(input_text=word, n=11000) for word in corpus]
    pad = pad_sequences(sequences=one_hot_word, maxlen=300, padding='pre')
    return pad

nltk.download('stopwords')


stop_words = set(stopwords.words('english'))


def sentence_cleaning(sentence, vocab_size=11000, max_len=300):
    stemmer = PorterStemmer()
    text = re.sub("[^a-zA-Z]", " ", sentence)
    text = text.lower()
    text = text.split()
    text = [stemmer.stem(word) for word in text if word not in stop_words]
    text = " ".join(text)

    one_hot_word = one_hot(input_text=text, n=vocab_size)
    padded = pad_sequences([one_hot_word], maxlen=max_len, padding='pre')

    return padded

# Test sentences
sentences = [
    "i feel strong and good overall",
    "im grabbing a minute to post i feel greedy wrong",
    "He was speechles when he found out he was accepted to this new job",
    "This is outrageous, how can you talk like that?",
    "I feel like im all alone in this world",
    "He is really sweet and caring",
    "You made me very crazy",
    "i am ever feeling nostalgic about the fireplace i will know that it is still on the property",
    "i am feeling grouchy",
    "He hates you"
]

# Load model and predict
for sentence in sentences:
    print(sentence)
    cleaned_sentence = sentence_cleaning(sentence)
    result = lb.inverse_transform(np.argmax(model.predict(cleaned_sentence), axis=-1))[0]
    proba = np.max(model.predict(cleaned_sentence))
    print(f"{result} : {proba:.4f}\n")

model.save('model1.h5')

# Save the LabelEncoder
with open('lb1.pkl', 'wb') as f:
    pickle.dump(lb, f)

# Save vocabulary size and max length
vocab_info = {'vocab_size': 11000, 'max_len': 300}
with open('vocab_info.pkl', 'wb') as f:
    pickle.dump(vocab_info, f)

# use this version
import tensorflow
import keras
print(keras.__version__)
print(tensorflow.__version__)

