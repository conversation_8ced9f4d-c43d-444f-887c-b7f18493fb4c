import streamlit as st
import numpy as np
import pickle
import re
import nltk
from nltk.corpus import stopwords
from nltk.stem import PorterStemmer
from tensorflow.keras.preprocessing.text import one_hot
from tensorflow.keras.preprocessing.sequence import pad_sequences
from tensorflow.keras.models import load_model

# Download stopwords if not already
try:
    nltk.data.find('corpora/stopwords')
except LookupError:
    nltk.download('stopwords')

# Page configuration
st.set_page_config(
    page_title="Emotion Detection App",
    page_icon="😊",
    layout="centered"
)

# Simple CSS styling
st.markdown("""
<style>
.main-title {
    text-align: center;
    color: #2E86AB;
    font-size: 2.5rem;
    margin-bottom: 1rem;
}
.subtitle {
    text-align: center;
    color: #666;
    font-size: 1.1rem;
    margin-bottom: 2rem;
}
.result-box {
    background-color: #f8f9fa;
    padding: 1.5rem;
    border-radius: 10px;
    border: 2px solid #e9ecef;
    margin: 1rem 0;
    text-align: center;
}
.emotion-text {
    font-size: 2rem;
    font-weight: bold;
    color: #28a745;
    margin-bottom: 0.5rem;
}
.confidence-text {
    font-size: 1.2rem;
    color: #6c757d;
}
</style>
""", unsafe_allow_html=True)

@st.cache_resource
def load_resources():
    """Load model and resources"""
    try:
        # Load model with compatibility handling
        try:
            model = load_model('model1.h5', compile=False)
            model.compile(optimizer='adam', loss='categorical_crossentropy', metrics=['accuracy'])
        except:
            model = load_model('model1.h5')

        # Load label encoder
        with open('lb1.pkl', 'rb') as f:
            lb = pickle.load(f)

        # Load vocabulary info
        with open('vocab_info.pkl', 'rb') as f:
            vocab_info = pickle.load(f)

        return model, lb, vocab_info
    except Exception as e:
        st.error(f"Error loading model: {str(e)}")
        return None, None, None

def clean_text(sentence, vocab_size, max_len):
    """Clean and preprocess text"""
    stop_words = set(stopwords.words('english'))
    stemmer = PorterStemmer()

    # Clean text
    text = re.sub("[^a-zA-Z]", " ", sentence)
    text = text.lower()
    text = text.split()
    text = [stemmer.stem(word) for word in text if word not in stop_words]
    text = " ".join(text)

    # Convert to sequences
    one_hot_word = one_hot(text, vocab_size)
    padded = pad_sequences([one_hot_word], maxlen=max_len, padding='pre')

    return padded

# Main app
def main():
    # Title
    st.markdown('<h1 class="main-title">😊 Emotion Detection App</h1>', unsafe_allow_html=True)
    st.markdown('<p class="subtitle">Analyze the emotion in your text using AI</p>', unsafe_allow_html=True)

    # Load model
    model, lb, vocab_info = load_resources()

    if model is None:
        st.error("❌ Could not load the model. Please check if all files exist.")
        st.stop()

    # Input section
    st.subheader("📝 Enter Your Text")
    user_input = st.text_area(
        "Type your message here:",
        placeholder="e.g., I am feeling really happy today!",
        height=100
    )

    # Example buttons
    st.subheader("🎯 Try Examples")
    col1, col2, col3 = st.columns(3)

    with col1:
        if st.button("😊 Happy"):
            user_input = "I am feeling really happy and excited today!"

    with col2:
        if st.button("😢 Sad"):
            user_input = "I feel so sad and lonely right now."

    with col3:
        if st.button("😠 Angry"):
            user_input = "This makes me so angry and frustrated!"

    # Predict button
    if st.button("🔮 Predict Emotion", type="primary"):
        if user_input.strip():
            try:
                with st.spinner("Analyzing emotion..."):
                    # Clean and preprocess text
                    cleaned = clean_text(user_input, vocab_info['vocab_size'], vocab_info['max_len'])

                    # Make prediction
                    prediction = model.predict(cleaned, verbose=0)
                    predicted_emotion = lb.inverse_transform([np.argmax(prediction, axis=1)[0]])[0]
                    confidence = np.max(prediction)

                    # Display result
                    st.markdown("---")
                    st.markdown(
                        f"""
                        <div class="result-box">
                            <div class="emotion-text">{predicted_emotion.title()}</div>
                            <div class="confidence-text">Confidence: {confidence:.1%}</div>
                        </div>
                        """,
                        unsafe_allow_html=True
                    )

                    # Show confidence bar
                    st.progress(confidence)

            except Exception as e:
                st.error(f"Error during prediction: {str(e)}")
        else:
            st.warning("⚠️ Please enter some text to analyze.")

if __name__ == "__main__":
    main()

def main():
    # Header
    st.markdown('<h1 class="main-header">🎭 Emotion Detection App</h1>', unsafe_allow_html=True)
    st.markdown("---")
    
    # Load model and components
    model, lb, vocab_info = load_resources()
    
    if model is None:
        st.error("❌ Failed to load the emotion detection model!")
        
        with st.expander("🔧 Troubleshooting Steps"):
            st.markdown("""
            **Possible solutions:**
            
            1. **Version Compatibility Issue**: Your model was saved with a different Keras/TensorFlow version.
               - Try downgrading: `pip install tensorflow==2.10.0 keras==2.10.0`
               - Or upgrade: `pip install --upgrade tensorflow keras`
            
            2. **Missing Files**: Ensure these files are in your app directory:
               - `model1.h5` (your trained model)
               - `lb1.pkl` (label encoder)
               - `vocab_info.pkl` (vocabulary info) - optional
            
            3. **Retrain Model**: Re-run your training script with current Keras version:
               ```python
               # Add this before saving your model
               model.save('model1.h5', save_format='h5')
               # Or try SavedModel format
               model.save('model_savedformat')
               ```
            
            4. **Alternative Format**: Try saving in SavedModel format instead of H5:
               ```python
               model.save('emotion_model')  # Creates a directory
               ```
            """)
        
        st.info("Please fix the model loading issue and restart the app.")
        st.stop()
    
    # Continue with the rest of the app...
    # Sidebar with information
    with st.sidebar:
        st.header("📋 About")
        st.info("This app uses a deep learning LSTM model to detect emotions in text.")
        
        st.header("🎯 Detected Emotions")
        if lb is not None:
            emotions_list = lb.inverse_transform(range(len(lb.classes_)))
            for emotion in emotions_list:
                st.write(f"• {emotion.title()}")
        
        st.header("💡 Tips")
        st.write("• Write natural sentences")
        st.write("• Express your feelings clearly")
        st.write("• Try different emotional contexts")
        
        # Model info
        st.header("🔧 Model Info")
        if vocab_info:
            st.write(f"• Vocabulary Size: {vocab_info['vocab_size']:,}")
            st.write(f"• Max Sequence Length: {vocab_info['max_len']}")
    
    # Main content
    col1, col2 = st.columns([2, 1])
    
    with col1:
        st.header("✍️ Enter Your Text")
        
        # Text input methods
        input_method = st.radio("Choose input method:", ["Type text", "Select example"])
        
        if input_method == "Type text":
            user_text = st.text_area(
                "Enter your text here:",
                placeholder="Type something that expresses an emotion...",
                height=100
            )
        else:
            example_texts = [
                "I feel strong and good overall",
                "I'm grabbing a minute to post I feel greedy wrong",
                "He was speechless when he found out he was accepted to this new job",
                "This is outrageous, how can you talk like that?",
                "I feel like I'm all alone in this world",
                "He is really sweet and caring",
                "You made me very crazy",
                "I am ever feeling nostalgic about the fireplace",
                "I am feeling grouchy",
                "He hates you"
            ]
            user_text = st.selectbox("Select an example:", [""] + example_texts)
        
        # Predict button
        if st.button("🔮 Predict Emotion", type="primary", use_container_width=True):
            if user_text and user_text.strip():
                try:
                    with st.spinner("Analyzing emotion..."):
                        emotion, confidence, all_probs = predict_emotion(user_text, model, lb, vocab_info)
                    
                    if emotion:
                        # Display main prediction
                        st.markdown('<div class="prediction-box">', unsafe_allow_html=True)
                        st.markdown(f'<div class="emotion-result">Predicted Emotion: {emotion.title()}</div>', unsafe_allow_html=True)
                        st.markdown(f'<div class="confidence-score">Confidence: {confidence:.2%}</div>', unsafe_allow_html=True)
                        st.markdown('</div>', unsafe_allow_html=True)
                        
                        # Store results in session state for visualization
                        st.session_state['last_prediction'] = {
                            'text': user_text,
                            'emotion': emotion,
                            'confidence': confidence,
                            'all_probs': all_probs
                        }
                    else:
                        st.error("Failed to predict emotion. Please try again.")
                except Exception as e:
                    st.error(f"Error during prediction: {str(e)}")
            else:
                st.warning("Please enter some text to analyze!")
    
    with col2:
        st.header("📊 Results")
        
        # Display visualization if prediction exists
        if 'last_prediction' in st.session_state:
            pred = st.session_state['last_prediction']
            
            # Create probability chart
            emotions = list(pred['all_probs'].keys())
            probabilities = list(pred['all_probs'].values())
            
            # Sort by probability for better visualization
            sorted_data = sorted(zip(emotions, probabilities), key=lambda x: x[1], reverse=True)
            emotions_sorted, probs_sorted = zip(*sorted_data)
            
            # Create bar chart
            fig = px.bar(
                x=probs_sorted,
                y=emotions_sorted,
                orientation='h',
                title="Emotion Probabilities",
                labels={'x': 'Probability', 'y': 'Emotion'},
                color=probs_sorted,
                color_continuous_scale='viridis'
            )
            fig.update_layout(height=400)
            st.plotly_chart(fig, use_container_width=True)
            
            # Show top 3 predictions
            st.subheader("Top 3 Predictions:")
            for i, (emotion, prob) in enumerate(sorted_data[:3]):
                st.write(f"{i+1}. **{emotion.title()}**: {prob:.2%}")
    
    # Batch prediction section
    st.markdown("---")
    st.header("📝 Batch Prediction")
    
    batch_text = st.text_area(
        "Enter multiple sentences (one per line):",
        placeholder="I am happy today\nThis makes me angry\nI feel sad about this",
        height=100
    )
    
    if st.button("🔮 Predict All", type="secondary"):
        if batch_text.strip():
            sentences = [line.strip() for line in batch_text.split('\n') if line.strip()]
            
            if sentences:
                results = []
                progress_bar = st.progress(0)
                
                try:
                    for i, sentence in enumerate(sentences):
                        emotion, confidence, _ = predict_emotion(sentence, model, lb, vocab_info)
                        if emotion:
                            results.append({
                                'Text': sentence,
                                'Emotion': emotion.title(),
                                'Confidence': f"{confidence:.2%}"
                            })
                        progress_bar.progress((i + 1) / len(sentences))
                    
                    if results:
                        df = pd.DataFrame(results)
                        st.dataframe(df, use_container_width=True)
                        
                        # Download option
                        csv = df.to_csv(index=False)
                        st.download_button(
                            label="📥 Download Results as CSV",
                            data=csv,
                            file_name="emotion_predictions.csv",
                            mime="text/csv"
                        )
                    else:
                        st.warning("No valid predictions were made.")
                        
                except Exception as e:
                    st.error(f"Error during batch prediction: {str(e)}")
        else:
            st.warning("Please enter some text to analyze!")
if __name__ == "__main__":
    main()