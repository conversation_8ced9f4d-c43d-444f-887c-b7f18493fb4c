import streamlit as st
import tensorflow as tf
from tensorflow.keras.models import load_model
from tensorflow.keras.preprocessing.text import one_hot
from tensorflow.keras.preprocessing.sequence import pad_sequences
import pickle
import numpy as np
import re
from nltk.corpus import stopwords
from nltk.stem import PorterStemmer
import nltk
import pandas as pd
import plotly.express as px
import plotly.graph_objects as go

# Download NLTK data if not already present
try:
    nltk.data.find('corpora/stopwords')
except LookupError:
    nltk.download('stopwords')

# Page configuration
st.set_page_config(
    page_title="Emotion Detection App",
    page_icon="😊",
    layout="wide",
    initial_sidebar_state="expanded"
)

# Custom CSS for better styling
st.markdown("""
<style>
.main-header {
    font-size: 3rem;
    color: #1f77b4;
    text-align: center;
    margin-bottom: 2rem;
}
.prediction-box {
    background-color: #f0f2f6;
    padding: 1rem;
    border-radius: 10px;
    border-left: 5px solid #1f77b4;
    margin: 1rem 0;
}
.emotion-result {
    font-size: 2rem;
    font-weight: bold;
    color: #2e8b57;
}
.confidence-score {
    font-size: 1.2rem;
    color: #666;
}
</style>
""", unsafe_allow_html=True)

@st.cache_resource
def load_model_and_encoder():
    """Load the trained model, label encoder, and vocabulary info"""
    try:
        # Try different methods to load the model
        try:
            # Method 1: Try loading with compile=False to avoid optimizer issues
            model = load_model('model1.h5', compile=False)
            # Recompile the model with current Keras version
            model.compile(optimizer='adam', loss='categorical_crossentropy', metrics=['accuracy'])
        except Exception as e1:
            st.warning(f"Failed to load with compile=False: {e1}")
            try:
                # Method 2: Try loading with custom objects
                import tensorflow.keras.utils as utils
                model = load_model('model1.h5', custom_objects={'Orthogonal': tf.keras.initializers.Orthogonal})
            except Exception as e2:
                st.warning(f"Failed to load with custom objects: {e2}")
                # Method 3: Rebuild the model architecture
                st.info("Attempting to rebuild model architecture...")
                model = rebuild_model()
                if model is None:
                    raise Exception("Could not rebuild model")
        
        # Load label encoder
        with open('lb1.pkl', 'rb') as f:
            lb = pickle.load(f)
        
        # Load vocabulary info
        try:
            with open('vocab_info.pkl', 'rb') as f:
                vocab_info = pickle.load(f)
        except FileNotFoundError:
            # Use default values if vocab_info.pkl doesn't exist
            vocab_info = {'vocab_size': 11000, 'max_len': 300}
            st.info("Using default vocabulary settings (vocab_size=11000, max_len=300)")
            
        return model, lb, vocab_info
        
    except FileNotFoundError as e:
        st.error(f"Model files not found: {e}")
        st.info("Please ensure 'model1.h5' and 'lb1.pkl' are in the same directory as this script.")
        return None, None, None
    except Exception as e:
        st.error(f"Error loading model: {e}")
        st.info("There seems to be a compatibility issue with the saved model. Try rebuilding the model with the current Keras version.")
        return None, None, None

def rebuild_model():
    """Rebuild the model architecture if loading fails"""
    try:
        from keras.models import Sequential
        from keras.layers import Embedding, LSTM, Dense, Dropout
        
        model = Sequential()
        model.add(Embedding(input_dim=11000, output_dim=150, input_length=300))
        model.add(Dropout(0.2))
        model.add(LSTM(128))
        model.add(Dropout(0.2))
        model.add(Dense(64, activation='sigmoid'))
        model.add(Dropout(0.2))
        model.add(Dense(6, activation='softmax'))
        model.compile(optimizer='adam', loss='categorical_crossentropy', metrics=['accuracy'])
        
        # Try to load weights if available
        try:
            model.load_weights('model1.h5')
            st.success("Successfully rebuilt model and loaded weights!")
        except:
            st.warning("Could not load pre-trained weights. Model will need to be retrained.")
            return None
            
        return model
    except Exception as e:
        st.error(f"Could not rebuild model: {e}")
        return None

def sentence_cleaning(sentence, vocab_size=11000, max_len=300):
    """Clean and preprocess the input sentence"""
    stop_words = set(stopwords.words('english'))
    stemmer = PorterStemmer()
    
    # Clean text
    text = re.sub("[^a-zA-Z]", " ", sentence)
    text = text.lower()
    text = text.split()
    text = [stemmer.stem(word) for word in text if word not in stop_words]
    text = " ".join(text)
    
    # Convert to one-hot encoding
    one_hot_word = one_hot(input_text=text, n=vocab_size)
    padded = pad_sequences([one_hot_word], maxlen=max_len, padding='pre')
    
    return padded

def predict_emotion(text, model, lb, vocab_info):
    """Predict emotion for given text"""
    if not text.strip():
        return None, None, None
    
    # Clean and preprocess text
    cleaned_text = sentence_cleaning(text, vocab_info['vocab_size'], vocab_info['max_len'])
    
    # Make prediction
    prediction = model.predict(cleaned_text, verbose=0)
    predicted_class = np.argmax(prediction, axis=-1)[0]
    confidence = np.max(prediction)
    
    # Get emotion label
    emotion = lb.inverse_transform([predicted_class])[0]
    
    # Get all probabilities for visualization
    all_emotions = lb.inverse_transform(range(len(lb.classes_)))
    probabilities = prediction[0]
    
    return emotion, confidence, dict(zip(all_emotions, probabilities))

def main():
    # Header
    st.markdown('<h1 class="main-header">🎭 Emotion Detection App</h1>', unsafe_allow_html=True)
    st.markdown("---")
    
    # Load model and components
    model, lb, vocab_info = load_model_and_encoder()
    
    if model is None:
        st.error("❌ Failed to load the emotion detection model!")
        
        with st.expander("🔧 Troubleshooting Steps"):
            st.markdown("""
            **Possible solutions:**
            
            1. **Version Compatibility Issue**: Your model was saved with a different Keras/TensorFlow version.
               - Try downgrading: `pip install tensorflow==2.10.0 keras==2.10.0`
               - Or upgrade: `pip install --upgrade tensorflow keras`
            
            2. **Missing Files**: Ensure these files are in your app directory:
               - `model1.h5` (your trained model)
               - `lb1.pkl` (label encoder)
               - `vocab_info.pkl` (vocabulary info) - optional
            
            3. **Retrain Model**: Re-run your training script with current Keras version:
               ```python
               # Add this before saving your model
               model.save('model1.h5', save_format='h5')
               # Or try SavedModel format
               model.save('model_savedformat')
               ```
            
            4. **Alternative Format**: Try saving in SavedModel format instead of H5:
               ```python
               model.save('emotion_model')  # Creates a directory
               ```
            """)
        
        st.info("Please fix the model loading issue and restart the app.")
        st.stop()
    
    # Continue with the rest of the app...
    # Sidebar with information
    with st.sidebar:
        st.header("📋 About")
        st.info("This app uses a deep learning LSTM model to detect emotions in text.")
        
        st.header("🎯 Detected Emotions")
        if lb is not None:
            emotions_list = lb.inverse_transform(range(len(lb.classes_)))
            for emotion in emotions_list:
                st.write(f"• {emotion.title()}")
        
        st.header("💡 Tips")
        st.write("• Write natural sentences")
        st.write("• Express your feelings clearly")
        st.write("• Try different emotional contexts")
        
        # Model info
        st.header("🔧 Model Info")
        if vocab_info:
            st.write(f"• Vocabulary Size: {vocab_info['vocab_size']:,}")
            st.write(f"• Max Sequence Length: {vocab_info['max_len']}")
    
    # Main content
    col1, col2 = st.columns([2, 1])
    
    with col1:
        st.header("✍️ Enter Your Text")
        
        # Text input methods
        input_method = st.radio("Choose input method:", ["Type text", "Select example"])
        
        if input_method == "Type text":
            user_text = st.text_area(
                "Enter your text here:",
                placeholder="Type something that expresses an emotion...",
                height=100
            )
        else:
            example_texts = [
                "I feel strong and good overall",
                "I'm grabbing a minute to post I feel greedy wrong",
                "He was speechless when he found out he was accepted to this new job",
                "This is outrageous, how can you talk like that?",
                "I feel like I'm all alone in this world",
                "He is really sweet and caring",
                "You made me very crazy",
                "I am ever feeling nostalgic about the fireplace",
                "I am feeling grouchy",
                "He hates you"
            ]
            user_text = st.selectbox("Select an example:", [""] + example_texts)
        
        # Predict button
        if st.button("🔮 Predict Emotion", type="primary", use_container_width=True):
            if user_text and user_text.strip():
                try:
                    with st.spinner("Analyzing emotion..."):
                        emotion, confidence, all_probs = predict_emotion(user_text, model, lb, vocab_info)
                    
                    if emotion:
                        # Display main prediction
                        st.markdown('<div class="prediction-box">', unsafe_allow_html=True)
                        st.markdown(f'<div class="emotion-result">Predicted Emotion: {emotion.title()}</div>', unsafe_allow_html=True)
                        st.markdown(f'<div class="confidence-score">Confidence: {confidence:.2%}</div>', unsafe_allow_html=True)
                        st.markdown('</div>', unsafe_allow_html=True)
                        
                        # Store results in session state for visualization
                        st.session_state['last_prediction'] = {
                            'text': user_text,
                            'emotion': emotion,
                            'confidence': confidence,
                            'all_probs': all_probs
                        }
                    else:
                        st.error("Failed to predict emotion. Please try again.")
                except Exception as e:
                    st.error(f"Error during prediction: {str(e)}")
            else:
                st.warning("Please enter some text to analyze!")
    
    with col2:
        st.header("📊 Results")
        
        # Display visualization if prediction exists
        if 'last_prediction' in st.session_state:
            pred = st.session_state['last_prediction']
            
            # Create probability chart
            emotions = list(pred['all_probs'].keys())
            probabilities = list(pred['all_probs'].values())
            
            # Sort by probability for better visualization
            sorted_data = sorted(zip(emotions, probabilities), key=lambda x: x[1], reverse=True)
            emotions_sorted, probs_sorted = zip(*sorted_data)
            
            # Create bar chart
            fig = px.bar(
                x=probs_sorted,
                y=emotions_sorted,
                orientation='h',
                title="Emotion Probabilities",
                labels={'x': 'Probability', 'y': 'Emotion'},
                color=probs_sorted,
                color_continuous_scale='viridis'
            )
            fig.update_layout(height=400)
            st.plotly_chart(fig, use_container_width=True)
            
            # Show top 3 predictions
            st.subheader("Top 3 Predictions:")
            for i, (emotion, prob) in enumerate(sorted_data[:3]):
                st.write(f"{i+1}. **{emotion.title()}**: {prob:.2%}")
    
    # Batch prediction section
    st.markdown("---")
    st.header("📝 Batch Prediction")
    
    batch_text = st.text_area(
        "Enter multiple sentences (one per line):",
        placeholder="I am happy today\nThis makes me angry\nI feel sad about this",
        height=100
    )
    
    if st.button("🔮 Predict All", type="secondary"):
        if batch_text.strip():
            sentences = [line.strip() for line in batch_text.split('\n') if line.strip()]
            
            if sentences:
                results = []
                progress_bar = st.progress(0)
                
                try:
                    for i, sentence in enumerate(sentences):
                        emotion, confidence, _ = predict_emotion(sentence, model, lb, vocab_info)
                        if emotion:
                            results.append({
                                'Text': sentence,
                                'Emotion': emotion.title(),
                                'Confidence': f"{confidence:.2%}"
                            })
                        progress_bar.progress((i + 1) / len(sentences))
                    
                    if results:
                        df = pd.DataFrame(results)
                        st.dataframe(df, use_container_width=True)
                        
                        # Download option
                        csv = df.to_csv(index=False)
                        st.download_button(
                            label="📥 Download Results as CSV",
                            data=csv,
                            file_name="emotion_predictions.csv",
                            mime="text/csv"
                        )
                    else:
                        st.warning("No valid predictions were made.")
                        
                except Exception as e:
                    st.error(f"Error during batch prediction: {str(e)}")
        else:
            st.warning("Please enter some text to analyze!")
if __name__ == "__main__":
    main()