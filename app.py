# app.py
import streamlit as st
import numpy as np
import pickle
import re
import nltk
from nltk.corpus import stopwords
from nltk.stem import PorterStemmer
from tensorflow.keras.preprocessing.text import one_hot
from tensorflow.keras.preprocessing.sequence import pad_sequences
from tensorflow.keras.models import load_model

# Download stopwords if not already
nltk.download('stopwords')

# Load model and label encoder
model = load_model('model1.h5')
with open('lb1.pkl', 'rb') as f:
    lb = pickle.load(f)

with open('vocab_info.pkl', 'rb') as f:
    vocab_info = pickle.load(f)

vocab_size = vocab_info['vocab_size']
max_len = vocab_info['max_len']

# Stopwords and stemmer
stop_words = set(stopwords.words('english'))
stemmer = PorterStemmer()

# Text cleaning function
def sentence_cleaning(sentence):
    text = re.sub("[^a-zA-Z]", " ", sentence)
    text = text.lower()
    text = text.split()
    text = [stemmer.stem(word) for word in text if word not in stop_words]
    text = " ".join(text)
    one_hot_word = one_hot(text, vocab_size)
    padded = pad_sequences([one_hot_word], maxlen=max_len, padding='pre')
    return padded

# Streamlit UI
st.title("🧠 Emotion Detection App")
st.write("Enter a sentence below and the model will predict the emotion behind it.")

user_input = st.text_area("📝 Type your sentence here:", "")

if st.button("Predict Emotion"):
    if user_input.strip() == "":
        st.warning("Please enter a valid sentence.")
    else:
        cleaned = sentence_cleaning(user_input)
        prediction = model.predict(cleaned)
        predicted_emotion = lb.inverse_transform([np.argmax(prediction, axis=1)[0]])[0]
        confidence = np.max(prediction)

        st.subheader("🎯 Predicted Emotion:")
        st.success(f"{predicted_emotion} ({confidence * 100:.2f}% confidence)")
