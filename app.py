# app.py
import streamlit as st
import numpy as np
import pickle
import re
import nltk
from nltk.corpus import stopwords
from nltk.stem import PorterStemmer
from tensorflow.keras.preprocessing.text import one_hot
from tensorflow.keras.preprocessing.sequence import pad_sequences
from tensorflow.keras.models import load_model
import os
import plotly.express as px
import pandas as pd

# Configure page
st.set_page_config(
    page_title="Emotion Detection App",
    page_icon="🧠",
    layout="wide",
    initial_sidebar_state="expanded"
)

# Custom CSS for better styling
st.markdown("""
<style>
    .main-header {
        font-size: 3rem;
        color: #1f77b4;
        text-align: center;
        margin-bottom: 2rem;
    }
    .emotion-card {
        background-color: #f0f2f6;
        padding: 1rem;
        border-radius: 10px;
        border-left: 5px solid #1f77b4;
        margin: 1rem 0;
    }
    .confidence-bar {
        background-color: #e0e0e0;
        border-radius: 10px;
        overflow: hidden;
        height: 20px;
        margin: 10px 0;
    }
    .stTextArea > div > div > textarea {
        font-size: 16px;
    }
</style>
""", unsafe_allow_html=True)

@st.cache_resource
def load_resources():
    """Load model and resources with caching for better performance"""
    try:
        # Download stopwords if not already
        nltk.download('stopwords', quiet=True)

        # Check if required files exist
        required_files = ['model1.h5', 'lb1.pkl', 'vocab_info.pkl']
        missing_files = [f for f in required_files if not os.path.exists(f)]

        if missing_files:
            st.error(f"Missing required files: {', '.join(missing_files)}")
            st.stop()

        # Load model with custom objects to handle version compatibility
        model_loaded = False
        model_files_to_try = ['model1.h5', 'model1_fixed.h5']

        for model_file in model_files_to_try:
            if not os.path.exists(model_file):
                continue

            try:
                # First try loading normally
                model = load_model(model_file)
                st.success(f"✅ Model loaded successfully from {model_file}")
                model_loaded = True
                break

            except Exception as model_error:
                st.warning(f"⚠️ Issue loading {model_file}. Trying compatibility fix...")
                try:
                    # Try loading with compile=False to avoid optimizer issues
                    model = load_model(model_file, compile=False)

                    # Recompile the model with current TensorFlow version
                    model.compile(
                        optimizer='adam',
                        loss='categorical_crossentropy',
                        metrics=['accuracy']
                    )
                    st.success(f"✅ Model loaded with compatibility fix from {model_file}!")
                    model_loaded = True
                    break

                except Exception as fallback_error:
                    st.warning(f"Could not load {model_file}: {str(model_error)}")
                    continue

        if not model_loaded:
            st.error("❌ Could not load any model file.")
            st.info("""
            **Troubleshooting steps:**

            1. **Run the compatibility fix script:**
               ```bash
               python fix_model_compatibility.py
               ```

            2. **Check TensorFlow version compatibility:**
               ```bash
               python -c "import tensorflow as tf; print(tf.__version__)"
               ```

            3. **Install compatible versions:**
               ```bash
               pip install tensorflow==2.10.0 keras==2.10.0
               ```

            4. **If all else fails, you may need to retrain the model**
            """)
            st.stop()

        with open('lb1.pkl', 'rb') as f:
            lb = pickle.load(f)

        with open('vocab_info.pkl', 'rb') as f:
            vocab_info = pickle.load(f)

        vocab_size = vocab_info['vocab_size']
        max_len = vocab_info['max_len']

        # Stopwords and stemmer
        stop_words = set(stopwords.words('english'))
        stemmer = PorterStemmer()

        return model, lb, vocab_size, max_len, stop_words, stemmer

    except Exception as e:
        st.error(f"Error loading resources: {str(e)}")
        st.error("""
        **Troubleshooting steps:**
        1. Check if all required files exist: model1.h5, lb1.pkl, vocab_info.pkl
        2. Verify TensorFlow installation: `pip install tensorflow==2.10.0`
        3. Try recreating the model if version mismatch persists
        """)
        st.stop()

def sentence_cleaning(sentence, vocab_size, max_len, stop_words, stemmer):
    """Clean and preprocess text for prediction"""
    try:
        text = re.sub("[^a-zA-Z]", " ", sentence)
        text = text.lower()
        text = text.split()
        text = [stemmer.stem(word) for word in text if word not in stop_words]
        text = " ".join(text)

        if not text.strip():
            return None

        one_hot_word = one_hot(text, vocab_size)
        padded = pad_sequences([one_hot_word], maxlen=max_len, padding='pre')
        return padded
    except Exception as e:
        st.error(f"Error processing text: {str(e)}")
        return None

def get_emotion_color(emotion):
    """Return color based on emotion type"""
    emotion_colors = {
        'joy': '#FFD700',
        'sadness': '#4169E1',
        'anger': '#DC143C',
        'fear': '#800080',
        'love': '#FF69B4',
        'surprise': '#FF8C00'
    }
    return emotion_colors.get(emotion.lower(), '#1f77b4')

def create_confidence_chart(predictions, emotions):
    """Create a confidence chart for all emotions"""
    df = pd.DataFrame({
        'Emotion': emotions,
        'Confidence': predictions[0] * 100
    })
    df = df.sort_values('Confidence', ascending=True)

    fig = px.bar(
        df,
        x='Confidence',
        y='Emotion',
        orientation='h',
        title='Confidence Scores for All Emotions',
        color='Confidence',
        color_continuous_scale='viridis'
    )
    fig.update_layout(height=400, showlegend=False)
    return fig

# Load resources
model, lb, vocab_size, max_len, stop_words, stemmer = load_resources()

# Sidebar
with st.sidebar:
    st.header("ℹ️ About")
    st.write("""
    This app uses a deep learning LSTM model to detect emotions in text.

    **Supported Emotions:**
    - Joy 😊
    - Sadness 😢
    - Anger 😠
    - Fear 😨
    - Love ❤️
    - Surprise 😲
    """)

    st.header("📊 Model Info")
    st.write(f"**Vocabulary Size:** {vocab_size:,}")
    st.write(f"**Max Sequence Length:** {max_len}")
    st.write(f"**Model Architecture:** LSTM")

    st.header("💡 Tips")
    st.write("""
    - Use complete sentences for better accuracy
    - The model works best with emotional expressions
    - Try different sentence structures
    """)

# Main content
st.markdown('<h1 class="main-header">🧠 Emotion Detection App</h1>', unsafe_allow_html=True)

st.markdown("""
<div style="text-align: center; font-size: 1.2rem; color: #666; margin-bottom: 2rem;">
    Analyze the emotional tone of your text using advanced deep learning
</div>
""", unsafe_allow_html=True)

# Create columns for better layout
col1, col2 = st.columns([2, 1])

with col1:
    st.subheader("📝 Enter Your Text")
    user_input = st.text_area(
        "Type your sentence here:",
        placeholder="e.g., I am feeling really happy today!",
        height=150,
        help="Enter any text and the model will analyze its emotional content"
    )

    # Prediction buttons
    col_btn1, col_btn2, col_btn3 = st.columns([1, 1, 2])

    with col_btn1:
        predict_btn = st.button("🎯 Predict Emotion", type="primary")

    with col_btn2:
        clear_btn = st.button("🗑️ Clear")

    if clear_btn:
        st.rerun()

with col2:
    st.subheader("🎲 Try Examples")
    example_texts = [
        "I am so excited about this new opportunity!",
        "I feel really sad and lonely today.",
        "This makes me so angry and frustrated!",
        "I'm scared about what might happen.",
        "I love spending time with my family.",
        "Wow, I didn't expect that to happen!"
    ]

    for i, example in enumerate(example_texts):
        if st.button(f"Example {i+1}", key=f"example_{i}"):
            st.session_state.example_text = example

# Use example text if selected
if 'example_text' in st.session_state:
    user_input = st.session_state.example_text
    del st.session_state.example_text
    st.rerun()

# Prediction logic
if predict_btn:
    if not user_input or user_input.strip() == "":
        st.warning("⚠️ Please enter a valid sentence to analyze.")
    else:
        with st.spinner("🔄 Analyzing emotion..."):
            try:
                # Clean and preprocess text
                cleaned = sentence_cleaning(user_input, vocab_size, max_len, stop_words, stemmer)

                if cleaned is None:
                    st.error("❌ Unable to process the text. Please try a different sentence.")
                else:
                    # Make prediction
                    prediction = model.predict(cleaned, verbose=0)
                    predicted_emotion = lb.inverse_transform([np.argmax(prediction, axis=1)[0]])[0]
                    confidence = np.max(prediction)

                    # Display results
                    st.markdown("---")
                    st.subheader("🎯 Prediction Results")

                    # Main prediction card
                    emotion_color = get_emotion_color(predicted_emotion)
                    st.markdown(f"""
                    <div class="emotion-card">
                        <h3 style="color: {emotion_color}; margin: 0;">
                            {predicted_emotion.title()}
                            <span style="font-size: 0.8em;">({confidence * 100:.1f}% confidence)</span>
                        </h3>
                        <div style="background-color: {emotion_color}; height: 10px; width: {confidence * 100}%; border-radius: 5px; margin-top: 10px;"></div>
                    </div>
                    """, unsafe_allow_html=True)

                    # Detailed confidence scores
                    if st.checkbox("📊 Show detailed confidence scores"):
                        emotions = lb.classes_
                        fig = create_confidence_chart(prediction, emotions)
                        st.plotly_chart(fig, use_container_width=True)

                        # Show raw scores
                        with st.expander("🔍 Raw Confidence Scores"):
                            scores_df = pd.DataFrame({
                                'Emotion': emotions,
                                'Confidence (%)': (prediction[0] * 100).round(2)
                            }).sort_values('Confidence (%)', ascending=False)
                            st.dataframe(scores_df, use_container_width=True)

                    # Text analysis
                    with st.expander("📝 Text Analysis"):
                        processed_words = user_input.lower().split()
                        cleaned_text = re.sub("[^a-zA-Z]", " ", user_input).lower().split()
                        cleaned_text = [word for word in cleaned_text if word not in stop_words and word.strip()]

                        col_analysis1, col_analysis2 = st.columns(2)
                        with col_analysis1:
                            st.write(f"**Original words:** {len(processed_words)}")
                            st.write(f"**Processed words:** {len(cleaned_text)}")

                        with col_analysis2:
                            st.write(f"**Characters:** {len(user_input)}")
                            st.write(f"**Sentences:** {len(user_input.split('.'))}")

                        if cleaned_text:
                            st.write("**Key words analyzed:**")
                            st.write(", ".join(cleaned_text[:10]))  # Show first 10 words

            except Exception as e:
                st.error(f"❌ An error occurred during prediction: {str(e)}")
                st.write("Please try again with a different sentence.")

# Footer
st.markdown("---")
st.markdown("""
<div style="text-align: center; color: #666; font-size: 0.9rem;">
    Built with ❤️ using Streamlit and TensorFlow | Emotion Detection with Deep Learning
</div>
""", unsafe_allow_html=True)
