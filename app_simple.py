import streamlit as st
import plotly.express as px
import pandas as pd
from emotion_predictor import EmotionPredictor
import time

# Page config
st.set_page_config(
    page_title="🎭 Emotion Detection App",
    page_icon="🎭",
    layout="wide",
    initial_sidebar_state="expanded"
)

# Custom CSS
st.markdown("""
<style>
    .main-header {
        font-size: 3rem;
        color: #1f77b4;
        text-align: center;
        margin-bottom: 2rem;
    }
    .emotion-card {
        padding: 1rem;
        border-radius: 10px;
        margin: 0.5rem 0;
        background-color: #f0f2f6;
        border-left: 5px solid #1f77b4;
    }
    .confidence-high { border-left-color: #28a745; }
    .confidence-medium { border-left-color: #ffc107; }
    .confidence-low { border-left-color: #dc3545; }
</style>
""", unsafe_allow_html=True)

@st.cache_resource
def load_emotion_model():
    """Load the emotion detection model (cached for performance)"""
    try:
        predictor = EmotionPredictor()
        return predictor, True
    except Exception as e:
        st.error(f"Error loading model: {e}")
        return None, False

def main():
    # Header
    st.markdown('<h1 class="main-header">🎭 Emotion Detection App</h1>', unsafe_allow_html=True)
    st.markdown("---")
    
    # Load model
    predictor, model_loaded = load_emotion_model()
    
    if not model_loaded:
        st.error("❌ Failed to load the emotion detection model!")
        st.info("Make sure 'emotion_model.h5' and 'label_encoder.pkl' are in the same directory.")
        return
    
    # Sidebar
    with st.sidebar:
        st.header("📊 App Info")
        st.info("This app uses a deep learning LSTM model to detect emotions in text.")
        
        st.header("🎯 Available Emotions")
        emotions = list(predictor.label_encoder.classes_)
        for emotion in emotions:
            st.write(f"• {emotion.title()}")
        
        st.header("💡 Tips")
        st.write("• Use clear, expressive language")
        st.write("• Longer sentences often work better")
        st.write("• Try different emotional contexts")
    
    # Main content
    col1, col2 = st.columns([2, 1])
    
    with col1:
        st.header("📝 Enter Your Text")
        
        # Text input options
        input_method = st.radio("Choose input method:", 
                               ["Type text", "Use example sentences"])
        
        if input_method == "Type text":
            user_input = st.text_area(
                "Enter your text here:",
                placeholder="Type something like 'I feel really happy today!' or 'This situation makes me so angry!'",
                height=100
            )
        else:
            example_sentences = [
                "I feel absolutely wonderful and excited about life!",
                "This situation makes me incredibly angry and frustrated!",
                "I'm really scared about what might happen next.",
                "I feel so sad and lonely, like nobody understands me.",
                "I'm disgusted by this behavior, it's completely unacceptable.",
                "What a lovely surprise! This made my day!",
                "I'm shocked and can't believe this actually happened."
            ]
            
            user_input = st.selectbox("Choose an example:", 
                                    [""] + example_sentences)
        
        # Prediction button
        col_btn1, col_btn2, col_btn3 = st.columns([1, 1, 1])
        
        with col_btn2:
            predict_button = st.button("🔍 Detect Emotion", 
                                     use_container_width=True,
                                     type="primary")
    
    with col2:
        st.header("📈 Results")
        result_placeholder = st.empty()
    
    # Prediction and results
    if predict_button and user_input and user_input.strip():
        with st.spinner("🤔 Analyzing emotion..."):
            # Add a small delay for better UX
            time.sleep(0.5)
            result = predictor.predict_emotion(user_input)
        
        # Display results in the right column
        with result_placeholder.container():
            if result['emotion'] != 'error':
                # Main emotion result
                confidence = result['confidence']
                emotion = result['emotion'].title()
                
                # Confidence styling
                if confidence > 0.7:
                    confidence_class = "confidence-high"
                elif confidence > 0.4:
                    confidence_class = "confidence-medium"
                else:
                    confidence_class = "confidence-low"
                
                st.markdown(f"""
                <div class="emotion-card {confidence_class}">
                    <h3>🎯 Detected Emotion</h3>
                    <h2>{emotion}</h2>
                    <p><strong>Confidence:</strong> {confidence:.1%}</p>
                </div>
                """, unsafe_allow_html=True)
                
                # Confidence meter
                st.progress(confidence)
                
                # All probabilities chart
                st.subheader("📊 All Emotions")
                prob_df = pd.DataFrame(
                    list(result['all_probabilities'].items()),
                    columns=['Emotion', 'Probability']
                )
                prob_df = prob_df.sort_values('Probability', ascending=True)
                
                fig = px.bar(prob_df, x='Probability', y='Emotion', 
                           orientation='h',
                           color='Probability',
                           color_continuous_scale='viridis')
                fig.update_layout(height=300, showlegend=False)
                st.plotly_chart(fig, use_container_width=True)
                
            else:
                st.error("❌ Error in emotion detection. Please try again.")
    
    elif predict_button and not user_input:
        st.warning("⚠️ Please enter some text to analyze!")
    
    # Footer
    st.markdown("---")
    st.markdown("*Built with Streamlit and TensorFlow/Keras LSTM model*")

if __name__ == "__main__":
    main()