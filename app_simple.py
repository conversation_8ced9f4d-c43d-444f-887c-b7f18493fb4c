import streamlit as st
import numpy as np
import pickle
import re
import nltk
from nltk.corpus import stopwords
from nltk.stem import PorterStemmer
from tensorflow.keras.preprocessing.text import one_hot
from tensorflow.keras.preprocessing.sequence import pad_sequences
from tensorflow.keras.models import load_model

# Download stopwords if not already
try:
    nltk.data.find('corpora/stopwords')
except LookupError:
    nltk.download('stopwords')

# Page configuration
st.set_page_config(
    page_title="Emotion Detection App",
    page_icon="😊",
    layout="centered"
)

# Simple CSS styling
st.markdown("""
<style>
.main-title {
    text-align: center;
    color: #2E86AB;
    font-size: 2.5rem;
    margin-bottom: 1rem;
}
.subtitle {
    text-align: center;
    color: #666;
    font-size: 1.1rem;
    margin-bottom: 2rem;
}
.result-box {
    background-color: #f8f9fa;
    padding: 1.5rem;
    border-radius: 10px;
    border: 2px solid #e9ecef;
    margin: 1rem 0;
    text-align: center;
}
.emotion-text {
    font-size: 2rem;
    font-weight: bold;
    color: #28a745;
    margin-bottom: 0.5rem;
}
.confidence-text {
    font-size: 1.2rem;
    color: #6c757d;
}
</style>
""", unsafe_allow_html=True)

@st.cache_resource
def load_resources():
    """Load model and resources"""
    try:
        # Load model with compatibility handling
        try:
            model = load_model('model1.h5', compile=False)
            model.compile(optimizer='adam', loss='categorical_crossentropy', metrics=['accuracy'])
        except:
            model = load_model('model1.h5')
        
        # Load label encoder
        with open('lb1.pkl', 'rb') as f:
            lb = pickle.load(f)
        
        # Load vocabulary info
        with open('vocab_info.pkl', 'rb') as f:
            vocab_info = pickle.load(f)
        
        return model, lb, vocab_info
    except Exception as e:
        st.error(f"Error loading model: {str(e)}")
        return None, None, None

def clean_text(sentence, vocab_size, max_len):
    """Clean and preprocess text"""
    stop_words = set(stopwords.words('english'))
    stemmer = PorterStemmer()
    
    # Clean text
    text = re.sub("[^a-zA-Z]", " ", sentence)
    text = text.lower()
    text = text.split()
    text = [stemmer.stem(word) for word in text if word not in stop_words]
    text = " ".join(text)
    
    # Convert to sequences
    one_hot_word = one_hot(text, vocab_size)
    padded = pad_sequences([one_hot_word], maxlen=max_len, padding='pre')
    
    return padded

# Main app
def main():
    # Title
    st.markdown('<h1 class="main-title">😊 Emotion Detection App</h1>', unsafe_allow_html=True)
    st.markdown('<p class="subtitle">Analyze the emotion in your text using AI</p>', unsafe_allow_html=True)
    
    # Load model
    model, lb, vocab_info = load_resources()
    
    if model is None:
        st.error("❌ Could not load the model. Please check if all files exist.")
        st.stop()
    
    # Input section
    st.subheader("📝 Enter Your Text")
    user_input = st.text_area(
        "Type your message here:",
        placeholder="e.g., I am feeling really happy today!",
        height=100
    )
    
    # Example buttons
    st.subheader("🎯 Try Examples")
    col1, col2, col3 = st.columns(3)
    
    with col1:
        if st.button("😊 Happy"):
            user_input = "I am feeling really happy and excited today!"
    
    with col2:
        if st.button("😢 Sad"):
            user_input = "I feel so sad and lonely right now."
    
    with col3:
        if st.button("😠 Angry"):
            user_input = "This makes me so angry and frustrated!"
    
    # Predict button
    if st.button("🔮 Predict Emotion", type="primary"):
        if user_input.strip():
            try:
                with st.spinner("Analyzing emotion..."):
                    # Clean and preprocess text
                    cleaned = clean_text(user_input, vocab_info['vocab_size'], vocab_info['max_len'])
                    
                    # Make prediction
                    prediction = model.predict(cleaned, verbose=0)
                    predicted_emotion = lb.inverse_transform([np.argmax(prediction, axis=1)[0]])[0]
                    confidence = np.max(prediction)
                    
                    # Display result
                    st.markdown("---")
                    st.markdown(
                        f"""
                        <div class="result-box">
                            <div class="emotion-text">{predicted_emotion.title()}</div>
                            <div class="confidence-text">Confidence: {confidence:.1%}</div>
                        </div>
                        """,
                        unsafe_allow_html=True
                    )
                    
                    # Show confidence bar
                    st.progress(confidence)
                    
            except Exception as e:
                st.error(f"Error during prediction: {str(e)}")
        else:
            st.warning("⚠️ Please enter some text to analyze.")

if __name__ == "__main__":
    main()
