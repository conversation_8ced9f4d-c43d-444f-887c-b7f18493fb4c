"""
<PERSON><PERSON><PERSON> to fix model compatibility issues by recreating the model architecture
and loading weights if possible.
"""

import tensorflow as tf
import numpy as np
import pickle
import os
from tensorflow.keras.models import Sequential
from tensorflow.keras.layers import Embedding, LSTM, Dense, Dropout

def recreate_model():
    """Recreate the model architecture based on the original training script"""
    
    print(f"TensorFlow version: {tf.__version__}")
    print(f"Keras version: {tf.keras.__version__}")
    
    # Load vocab info
    try:
        with open('vocab_info.pkl', 'rb') as f:
            vocab_info = pickle.load(f)
        vocab_size = vocab_info['vocab_size']  # Should be 11000
        max_len = vocab_info['max_len']        # Should be 300
        print(f"Vocab size: {vocab_size}, Max length: {max_len}")
    except Exception as e:
        print(f"Error loading vocab_info.pkl: {e}")
        # Use default values from the training script
        vocab_size = 11000
        max_len = 300
        print(f"Using default values - Vocab size: {vocab_size}, Max length: {max_len}")
    
    # Recreate the model architecture (from the training script)
    model = Sequential()
    model.add(Embedding(input_dim=vocab_size, output_dim=150, input_length=max_len))
    model.add(Dropout(0.2))
    model.add(LSTM(128))
    model.add(Dropout(0.2))
    model.add(Dense(64, activation='sigmoid'))
    model.add(Dropout(0.2))
    model.add(Dense(6, activation='softmax'))  # 6 emotions
    
    # Compile the model
    model.compile(
        optimizer='adam',
        loss='categorical_crossentropy',
        metrics=['accuracy']
    )
    
    print("Model architecture recreated successfully!")
    print(model.summary())
    
    # Try to load weights from the old model
    try:
        print("Attempting to load weights from existing model...")
        old_model = tf.keras.models.load_model('model1.h5', compile=False)
        
        # Copy weights layer by layer
        for i, layer in enumerate(model.layers):
            if i < len(old_model.layers):
                try:
                    layer.set_weights(old_model.get_layer(index=i).get_weights())
                    print(f"Loaded weights for layer {i}: {layer.name}")
                except Exception as e:
                    print(f"Could not load weights for layer {i}: {e}")
        
        print("Weights loaded successfully!")
        
    except Exception as e:
        print(f"Could not load weights from old model: {e}")
        print("Model created with random weights. You may need to retrain.")
    
    # Save the new compatible model
    model.save('model1_fixed.h5')
    print("New compatible model saved as 'model1_fixed.h5'")
    
    # Test the model with a sample input
    try:
        # Create a dummy input
        dummy_input = np.random.randint(0, vocab_size, size=(1, max_len))
        prediction = model.predict(dummy_input, verbose=0)
        print(f"Model test successful! Output shape: {prediction.shape}")
        print(f"Sample prediction: {prediction[0]}")
        
        # Check if we can load the label encoder
        with open('lb1.pkl', 'rb') as f:
            lb = pickle.load(f)
        print(f"Label encoder loaded. Classes: {lb.classes_}")
        
    except Exception as e:
        print(f"Model test failed: {e}")
    
    return model

def backup_original_model():
    """Backup the original model before fixing"""
    if os.path.exists('model1.h5') and not os.path.exists('model1_original_backup.h5'):
        import shutil
        shutil.copy2('model1.h5', 'model1_original_backup.h5')
        print("Original model backed up as 'model1_original_backup.h5'")

def replace_model_with_fixed():
    """Replace the original model with the fixed version"""
    if os.path.exists('model1_fixed.h5'):
        import shutil
        if os.path.exists('model1.h5'):
            shutil.move('model1.h5', 'model1_old.h5')
        shutil.move('model1_fixed.h5', 'model1.h5')
        print("Fixed model is now the main model (model1.h5)")
        print("Old model saved as 'model1_old.h5'")

if __name__ == "__main__":
    print("=== Model Compatibility Fix Script ===")
    print()
    
    # Backup original model
    backup_original_model()
    
    # Recreate model
    try:
        model = recreate_model()
        
        # Ask user if they want to replace the original
        response = input("\nDo you want to replace the original model with the fixed version? (y/n): ")
        if response.lower() in ['y', 'yes']:
            replace_model_with_fixed()
            print("\nModel replacement complete!")
        else:
            print("\nFixed model saved as 'model1_fixed.h5'. You can manually replace it later.")
            
        print("\nYou can now run the Streamlit app: streamlit run app.py")
        
    except Exception as e:
        print(f"Error during model recreation: {e}")
        print("\nIf this script fails, you may need to:")
        print("1. Retrain the model with your current TensorFlow version")
        print("2. Check that all required files exist")
        print("3. Verify your TensorFlow installation")
