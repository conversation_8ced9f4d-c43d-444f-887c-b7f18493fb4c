import numpy as np
import pickle
import re
import nltk
from nltk.corpus import stopwords
from nltk.stem import PorterStemmer
from tensorflow.keras.preprocessing.text import one_hot
from tensorflow.keras.preprocessing.sequence import pad_sequences
from tensorflow.keras.models import load_model

# Download stopwords if not already
try:
    nltk.data.find('corpora/stopwords')
except LookupError:
    nltk.download('stopwords')

class EmotionPredictor:
    def __init__(self):
        self.model = None
        self.label_encoder = None
        self.vocab_size = 11000  # Default vocab size
        self.max_len = 300       # Default max length
        self.load_resources()

    def load_resources(self):
        """Load model and resources with error handling"""
        try:
            # Load model with multiple fallback strategies
            model_loaded = False

            # Strategy 1: Load without compiling
            try:
                self.model = load_model('emotion_model.h5', compile=False)
                self.model.compile(optimizer='adam', loss='categorical_crossentropy', metrics=['accuracy'])
                model_loaded = True
                print("Model loaded successfully (strategy 1)")
            except Exception as e1:
                print(f"Strategy 1 failed: {e1}")

                # Strategy 2: Load with custom objects
                try:
                    from tensorflow.keras.initializers import Orthogonal
                    custom_objects = {'Orthogonal': Orthogonal}
                    self.model = load_model('emotion_model.h5', custom_objects=custom_objects)
                    model_loaded = True
                    print("Model loaded successfully (strategy 2)")
                except Exception as e2:
                    print(f"Strategy 2 failed: {e2}")

                    # Strategy 3: Try default loading
                    try:
                        self.model = load_model('emotion_model.h5')
                        model_loaded = True
                        print("Model loaded successfully (strategy 3)")
                    except Exception as e3:
                        print(f"All strategies failed. Last error: {e3}")

            if not model_loaded:
                return False

            # Load label encoder
            with open('label_encoder.pkl', 'rb') as f:
                self.label_encoder = pickle.load(f)

            print("All resources loaded successfully")
            return True
        except Exception as e:
            print(f"Error loading resources: {str(e)}")
            return False
    
    def clean_text(self, sentence):
        """Clean and preprocess text"""
        stop_words = set(stopwords.words('english'))
        stemmer = PorterStemmer()

        # Clean text
        text = re.sub("[^a-zA-Z]", " ", sentence)
        text = text.lower()
        text = text.split()
        text = [stemmer.stem(word) for word in text if word not in stop_words]
        text = " ".join(text)

        # Convert to sequences
        one_hot_word = one_hot(text, self.vocab_size)
        padded = pad_sequences([one_hot_word], maxlen=self.max_len, padding='pre')

        return padded
    
    def predict_emotion(self, text):
        """Predict emotion from text"""
        if self.model is None:
            return None, 0.0

        try:
            # Clean and preprocess text
            cleaned = self.clean_text(text)

            # Make prediction
            prediction = self.model.predict(cleaned, verbose=0)
            predicted_emotion = self.label_encoder.inverse_transform([np.argmax(prediction, axis=1)[0]])[0]
            confidence = np.max(prediction)

            return predicted_emotion, confidence
        except Exception as e:
            print(f"Error during prediction: {str(e)}")
            return None, 0.0
    
    def get_all_probabilities(self, text):
        """Get probabilities for all emotions"""
        if self.model is None:
            return {}

        try:
            # Clean and preprocess text
            cleaned = self.clean_text(text)

            # Make prediction
            prediction = self.model.predict(cleaned, verbose=0)
            probabilities = prediction[0]

            # Create emotion-probability mapping
            emotion_probs = {}
            for i, emotion in enumerate(self.label_encoder.classes_):
                emotion_probs[emotion] = float(probabilities[i])

            return emotion_probs
        except Exception as e:
            print(f"Error during prediction: {str(e)}")
            return {}
    
    def is_loaded(self):
        """Check if model is loaded successfully"""
        return self.model is not None and self.label_encoder is not None
